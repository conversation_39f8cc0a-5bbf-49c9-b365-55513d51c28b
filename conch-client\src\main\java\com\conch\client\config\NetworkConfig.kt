package com.conch.client.config

import android.content.Context
import android.content.SharedPreferences

/**
 * 网络配置管理类
 * 支持动态配置服务端地址和端口
 */
object NetworkConfig {

    // 默认配置 - 根据环境动态设置
    private const val DEFAULT_HOST = "***********"  // Android设备需要使用PC的实际IP
    private const val DEFAULT_PORT = 8080
    private const val DEFAULT_PROTOCOL = "http"

    // SharedPreferences 键名
    private const val PREFS_NAME = "network_config"
    private const val KEY_HOST = "server_host"
    private const val KEY_PORT = "server_port"
    private const val KEY_PROTOCOL = "server_protocol"

    private var prefs: SharedPreferences? = null

    /**
     * 初始化配置
     */
    fun init(context: Context) {
        // 使用ApplicationContext避免内存泄漏
        prefs = context.applicationContext.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)

        // 首次启动时，尝试从环境变量或系统属性读取配置
        initializeDefaultConfig()
    }

    /**
     * 初始化默认配置，支持从环境变量读取
     */
    private fun initializeDefaultConfig() {
        prefs?.let { preferences ->
            // 如果是首次启动，尝试从系统属性读取配置
            if (!preferences.contains(KEY_HOST)) {
                val envHost = System.getProperty("conch.server.host") ?: DEFAULT_HOST
                val envPort = System.getProperty("conch.server.port")?.toIntOrNull() ?: DEFAULT_PORT
                val envProtocol = System.getProperty("conch.server.protocol") ?: DEFAULT_PROTOCOL

                preferences.edit().apply {
                    putString(KEY_HOST, envHost)
                    putInt(KEY_PORT, envPort)
                    putString(KEY_PROTOCOL, envProtocol)
                    apply()
                }
            }
        }
    }

    /**
     * 获取服务端主机地址
     */
    fun getHost(): String {
        return prefs?.getString(KEY_HOST, DEFAULT_HOST) ?: DEFAULT_HOST
    }

    /**
     * 获取服务端端口
     */
    fun getPort(): Int {
        return prefs?.getInt(KEY_PORT, DEFAULT_PORT) ?: DEFAULT_PORT
    }

    /**
     * 获取协议
     */
    fun getProtocol(): String {
        return prefs?.getString(KEY_PROTOCOL, DEFAULT_PROTOCOL) ?: DEFAULT_PROTOCOL
    }

    /**
     * 获取完整的基础URL
     */
    fun getBaseUrl(): String {
        return "${getProtocol()}://${getHost()}:${getPort()}"
    }

    /**
     * 获取API基础URL
     */
    fun getApiBaseUrl(): String {
        return "${getBaseUrl()}/api/v1"
    }

    /**
     * 设置服务端配置
     */
    fun setServerConfig(host: String, port: Int, protocol: String = DEFAULT_PROTOCOL) {
        prefs?.edit()?.apply {
            putString(KEY_HOST, host)
            putInt(KEY_PORT, port)
            putString(KEY_PROTOCOL, protocol)
            apply()
        }
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefault() {
        prefs?.edit()?.clear()?.apply()
    }

    /**
     * 获取当前配置信息
     */
    fun getCurrentConfig(): Map<String, Any> {
        return mapOf(
            "host" to getHost(),
            "port" to getPort(),
            "protocol" to getProtocol(),
            "baseUrl" to getBaseUrl(),
        )
    }
}
