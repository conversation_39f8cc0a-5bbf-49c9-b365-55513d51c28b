package com.conch.client.network

import android.util.Log
import com.conch.client.config.NetworkConfig
import com.conch.client.model.CommandRequest
import com.conch.client.model.CommandResponse
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.json.JSONObject
import java.io.IOException
import java.net.HttpURLConnection
import java.net.SocketTimeoutException
import java.net.URL
import java.net.ConnectException
import java.net.UnknownHostException

/**
 * API客户端 - 处理与服务端的网络通信
 */
object ApiClient {
    
    private const val TAG = "ApiClient"
    
    // 超时配置
    private const val CONNECT_TIMEOUT = 5000
    private const val READ_TIMEOUT = 10000
    
    // 重试配置
    private const val MAX_RETRY_COUNT = 3
    private const val RETRY_DELAY = 1000L
    
    /**
     * 发送指令到服务端
     */
    suspend fun sendCommand(request: CommandRequest): NetworkResult<CommandResponse> {
        return withContext(Dispatchers.IO) {
            var lastException: NetworkException? = null

            for (attempt in 0 until MAX_RETRY_COUNT) {
                try {
                    Log.d(TAG, "发送指令请求 (尝试 ${attempt + 1}/$MAX_RETRY_COUNT)")

                    val response = performRequest(request)
                    Log.d(TAG, "请求成功: $response")
                    return@withContext NetworkResult.Success(response)

                } catch (e: NetworkException) {
                    lastException = e
                    Log.w(TAG, "请求失败 (尝试 ${attempt + 1}/$MAX_RETRY_COUNT): ${e.message}")

                    // 对于某些错误类型，不进行重试
                    if (e is NetworkException.ServerException && e.code in 400..499) {
                        Log.w(TAG, "客户端错误，停止重试")
                        break
                    }

                    if (attempt < MAX_RETRY_COUNT - 1) {
                        try {
                            kotlinx.coroutines.delay(RETRY_DELAY * (attempt + 1)) // 指数退避
                        } catch (ie: InterruptedException) {
                            Thread.currentThread().interrupt()
                            break
                        }
                    }
                } catch (e: Exception) {
                    lastException = NetworkException.UnknownException("未知错误", e)
                    Log.w(TAG, "未知错误 (尝试 ${attempt + 1}/$MAX_RETRY_COUNT): ${e.message}")

                    if (attempt < MAX_RETRY_COUNT - 1) {
                        try {
                            kotlinx.coroutines.delay(RETRY_DELAY * (attempt + 1))
                        } catch (ie: InterruptedException) {
                            Thread.currentThread().interrupt()
                            break
                        }
                    }
                }
            }

            Log.e(TAG, "所有重试都失败了", lastException)
            NetworkResult.Error(lastException ?: NetworkException.UnknownException())
        }
    }
    
    /**
     * 执行实际的网络请求
     */
    private fun performRequest(request: CommandRequest): CommandResponse {
        val url = URL("${NetworkConfig.getApiBaseUrl()}/voice/command")
        val connection = url.openConnection() as HttpURLConnection

        try {
            // 配置连接
            connection.apply {
                requestMethod = "POST"
                setRequestProperty("Content-Type", "application/json; charset=UTF-8")
                setRequestProperty("Accept", "application/json")
                setRequestProperty("Accept-Charset", "UTF-8")
                doOutput = true
                connectTimeout = CONNECT_TIMEOUT
                readTimeout = READ_TIMEOUT
            }

            // 构建请求JSON
            val requestJson = buildRequestJson(request)
            Log.d(TAG, "请求JSON: $requestJson")

            // 发送请求
            connection.outputStream.use { os ->
                os.write(requestJson.toByteArray(Charsets.UTF_8))
            }

            // 处理响应
            val responseCode = connection.responseCode
            if (responseCode == HttpURLConnection.HTTP_OK) {
                val responseText = connection.inputStream.bufferedReader(Charsets.UTF_8).readText()
                Log.d(TAG, "响应内容: $responseText")
                return parseResponse(responseText)
            } else {
                val errorText = connection.errorStream?.bufferedReader(Charsets.UTF_8)?.readText() ?: "未知错误"
                throw NetworkException.ServerException(responseCode, errorText)
            }

        } catch (e: SocketTimeoutException) {
            throw NetworkException.TimeoutException("请求超时")
        } catch (e: ConnectException) {
            throw NetworkException.NetworkUnavailableException("无法连接到服务器")
        } catch (e: UnknownHostException) {
            throw NetworkException.NetworkUnavailableException("无法解析服务器地址")
        } catch (e: NetworkException) {
            throw e // 重新抛出已知的网络异常
        } catch (e: Exception) {
            throw NetworkException.UnknownException("网络请求失败", e)
        } finally {
            connection.disconnect()
        }
    }
    
    /**
     * 构建请求JSON
     */
    private fun buildRequestJson(request: CommandRequest): String {
        // 使用字符串拼接方式构建JSON，避免Android JSONObject兼容性问题
        val textCommandJson = """
            {
                "text": "${request.text.replace("\"", "\\\"")}",
                "confidence": ${request.confidence},
                "timestamp": ${request.timestamp}
            }
        """.trimIndent()

        val installedAppsJson = request.deviceInfo.installedApps.joinToString(
            prefix = "[",
            postfix = "]",
            separator = ","
        ) { "\"${it.replace("\"", "\\\"")}\"" }

        val deviceInfoJson = """
            {
                "model": "${request.deviceInfo.model.replace("\"", "\\\"")}",
                "androidVersion": "${request.deviceInfo.androidVersion.replace("\"", "\\\"")}",
                "screenResolution": "${request.deviceInfo.screenResolution.replace("\"", "\\\"")}",
                "installedApps": $installedAppsJson
            }
        """.trimIndent()

        val requestJson = """
            {
                "textCommand": $textCommandJson,
                "deviceInfo": $deviceInfoJson
            }
        """.trimIndent()

        return requestJson
    }
    
    /**
     * 解析响应JSON
     */
    private fun parseResponse(responseText: String): CommandResponse {
        try {
            val json = JSONObject(responseText)
            return CommandResponse(
                sessionId = json.getString("sessionId"),
                state = json.getString("state"),
                message = json.getString("message"),
                estimatedDuration = json.optLong("estimatedDuration", 0)
            )
        } catch (e: Exception) {
            Log.e(TAG, "JSON解析失败: $responseText", e)
            throw NetworkException.ParseException("响应数据解析失败")
        }
    }
    
    /**
     * 检查网络连接
     */
    suspend fun checkConnection(): NetworkResult<Boolean> {
        return withContext(Dispatchers.IO) {
            try {
                val healthUrl = "${NetworkConfig.getApiBaseUrl()}/health"
                Log.d(TAG, "检查连接URL: $healthUrl")

                val url = URL(healthUrl)
                val connection = url.openConnection() as HttpURLConnection

                connection.apply {
                    requestMethod = "GET"
                    connectTimeout = CONNECT_TIMEOUT
                    readTimeout = READ_TIMEOUT
                    setRequestProperty("Accept", "application/json")
                }

                Log.d(TAG, "发送健康检查请求...")
                val responseCode = connection.responseCode
                Log.d(TAG, "健康检查响应码: $responseCode")

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    val responseText = connection.inputStream.bufferedReader(Charsets.UTF_8).readText()
                    Log.d(TAG, "健康检查响应: $responseText")
                }

                connection.disconnect()

                NetworkResult.Success(responseCode == HttpURLConnection.HTTP_OK)
            } catch (e: SocketTimeoutException) {
                Log.e(TAG, "连接检查超时: ${e.message}", e)
                NetworkResult.Error(NetworkException.TimeoutException("连接检查超时"))
            } catch (e: ConnectException) {
                Log.e(TAG, "连接检查失败: ${e.message}", e)
                NetworkResult.Error(NetworkException.NetworkUnavailableException("无法连接到服务器: ${e.message}"))
            } catch (e: UnknownHostException) {
                Log.e(TAG, "无法解析主机: ${e.message}", e)
                NetworkResult.Error(NetworkException.NetworkUnavailableException("无法解析服务器地址: ${e.message}"))
            } catch (e: Exception) {
                Log.e(TAG, "连接检查失败: ${e.message}", e)
                NetworkResult.Error(NetworkException.UnknownException("连接检查失败: ${e.message}", e))
            }
        }
    }
}
